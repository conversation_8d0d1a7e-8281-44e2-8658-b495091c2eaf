# 微信小程序H5跳转工具

基于微信官方2023年12月19日发布的URL Scheme和URL Link优化方案开发的PHP工具，支持通过H5页面跳转到微信小程序。

## 功能特性

- ✅ **明文URL Scheme**：无需调用接口，直接拼接生成（推荐使用）
- ✅ **加密URL Scheme**：通过微信接口生成，支持自定义参数
- ✅ **加密URL Link**：通过微信接口生成，支持微信外打开
- ✅ **Access Token缓存**：自动缓存和刷新access_token
- ✅ **错误处理**：完善的错误提示和日志记录
- ✅ **参数验证**：支持页面路径、查询参数、版本类型配置

## 文件结构

```
├── index.php          # 主页面，提供Web界面生成跳转链接
├── config.php         # 配置文件，包含小程序信息和工具函数
├── test.php           # 测试页面，验证功能是否正常
├── README.md          # 使用说明文档
├── cache/             # 缓存目录（自动创建）
│   └── access_token.json
└── logs/              # 日志目录（自动创建）
    └── wechat.log
```

## 快速开始

### 1. 配置小程序信息

编辑 `config.php` 文件，修改以下配置：

```php
define('WECHAT_APPID', 'your_appid_here');
define('WECHAT_APP_SECRET', 'your_app_secret_here');
```

### 2. 配置小程序后台

对于明文URL Scheme，需要在小程序管理后台进行配置：

1. 登录小程序管理后台
2. 进入「设置 → 隐私与安全 → 明文scheme拉起此小程序」
3. 添加允许通过明文scheme访问的页面路径

### 3. 部署到服务器

将所有文件上传到支持PHP的Web服务器，确保：
- PHP版本 >= 7.0
- 启用curl扩展
- 有文件写入权限（用于缓存和日志）

### 4. 访问页面

- 主页面：`http://your-domain.com/index.php`
- 测试页面：`http://your-domain.com/test.php`

## 使用方法

### Web界面使用

1. 打开 `index.php` 页面
2. 选择跳转类型（推荐使用明文URL Scheme）
3. 填写小程序页面路径（可选）
4. 填写查询参数（可选）
5. 选择版本类型（正式版/体验版/开发版）
6. 点击"生成跳转链接"按钮
7. 复制生成的链接或直接测试

### 代码调用示例

```php
require_once 'config.php';

// 创建实例
$wechat = new WechatMiniProgram('your_appid', 'your_app_secret');

// 生成明文URL Scheme（推荐）
$plainScheme = $wechat->generatePlainUrlScheme(
    'pages/detail/detail',  // 页面路径
    ['id' => '123'],        // 查询参数
    'release'               // 版本类型
);

// 生成加密URL Scheme
$encryptedScheme = $wechat->generateEncryptedUrlScheme(
    'pages/detail/detail',
    ['id' => '123'],
    'release'
);

// 生成加密URL Link
$encryptedLink = $wechat->generateEncryptedUrlLink(
    'pages/detail/detail',
    ['id' => '123'],
    'release'
);
```

## 三种跳转方式对比

| 特性 | 明文URL Scheme | 加密URL Scheme | 加密URL Link |
|------|----------------|----------------|--------------|
| 生成方式 | 直接拼接 | 调用接口 | 调用接口 |
| 生成限制 | 无限制 | 50万次/天 | 50万次/天 |
| 打开限制 | 300万次/天 | 300万次/天 | 300万次/天 |
| 一人一链 | 无限制 | 无限制 | 无限制 |
| 有效期 | 永久 | 永久 | 永久 |
| 场景值 | 1286 | 1065 | 1194/1167 |
| 推荐度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 场景值说明

小程序可以通过 `wx.getLaunchOptionsSync()` 获取场景值：

- **1286**：明文URL Scheme打开
- **1065**：加密URL Scheme打开  
- **1194**：加密URL Link在微信外打开
- **1167**：加密URL Link在微信内打开

## 注意事项

1. **主体限制**：仅支持非个人主体小程序使用
2. **页面配置**：明文URL Scheme需要在后台配置允许的页面路径
3. **打开限制**：每个小程序每天总打开次数上限300万次
4. **安全策略**：平台有防刷机制，避免被恶意大量访问
5. **参数限制**：自定义参数最大256个字符，需要URL编码

## 错误排查

### 常见错误码

- **40001**：AppSecret错误
- **40013**：AppID不合法
- **42001**：access_token超时
- **45009**：接口调用超过限制
- **85079**：小程序没有线上版本

### 调试方法

1. 查看 `logs/wechat.log` 日志文件
2. 访问 `test.php` 页面检查配置
3. 确认小程序后台配置正确
4. 检查网络连接和服务器权限

## 技术支持

如遇问题，请检查：

1. PHP环境和扩展是否正确安装
2. 小程序AppID和AppSecret是否正确
3. 服务器是否有文件读写权限
4. 网络是否能正常访问微信API

## 更新日志

- **v1.0.0**：初始版本，支持三种跳转方式
- 基于微信官方2023年12月19日优化方案开发

## 许可证

MIT License

# API使用文档

## 接口地址

```
POST/GET /api.php
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| type | string | 是 | 生成类型 | plain, encrypted_scheme, encrypted_link, all |
| path | string | 否 | 小程序页面路径 | pages/detail/detail |
| query | object/string | 否 | 查询参数 | {"id":"123"} 或 "id=123&name=test" |
| env_version | string | 否 | 版本类型，默认release | release, trial, develop |

## 响应格式

```json
{
    "success": true,
    "code": 0,
    "message": "生成成功",
    "data": {
        "plain_scheme": "weixin://dl/business/?appid=xxx&path=xxx&query=xxx&env_version=release"
    },
    "timestamp": 1703001234
}
```

## 使用示例

### 1. JavaScript/Ajax调用

```javascript
// 生成明文URL Scheme
fetch('/api.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        type: 'plain',
        path: 'pages/detail/detail',
        query: {
            id: '123',
            name: 'test'
        },
        env_version: 'release'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('生成的链接:', data.data.plain_scheme);
        // 可以直接使用链接跳转
        window.open(data.data.plain_scheme);
    } else {
        console.error('生成失败:', data.message);
    }
});

// 生成所有类型的链接
fetch('/api.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        type: 'all',
        path: 'pages/index/index',
        query: 'debug=1&source=api'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('明文Scheme:', data.data.plain_scheme);
        console.log('加密Scheme:', data.data.encrypted_scheme);
        console.log('加密Link:', data.data.encrypted_link);
    }
});
```

### 2. PHP调用

```php
// 使用cURL调用API
function callWechatAPI($type, $path = '', $query = [], $envVersion = 'release') {
    $url = 'http://your-domain.com/api.php';
    
    $data = [
        'type' => $type,
        'path' => $path,
        'query' => $query,
        'env_version' => $envVersion
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// 使用示例
$result = callWechatAPI('plain', 'pages/detail/detail', ['id' => '123']);
if ($result['success']) {
    echo "生成的链接: " . $result['data']['plain_scheme'];
} else {
    echo "错误: " . $result['message'];
}
```

### 3. Python调用

```python
import requests
import json

def call_wechat_api(type_name, path='', query=None, env_version='release'):
    url = 'http://your-domain.com/api.php'
    
    data = {
        'type': type_name,
        'path': path,
        'query': query or {},
        'env_version': env_version
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
result = call_wechat_api('plain', 'pages/detail/detail', {'id': '123'})
if result['success']:
    print(f"生成的链接: {result['data']['plain_scheme']}")
else:
    print(f"错误: {result['message']}")
```

### 4. GET请求方式

```bash
# 使用curl命令行
curl "http://your-domain.com/api.php?type=plain&path=pages/index/index&query=id%3D123&env_version=release"

# 浏览器直接访问
http://your-domain.com/api.php?type=plain&path=pages/detail/detail&query=id=123&name=test
```

### 5. jQuery调用

```javascript
$.ajax({
    url: '/api.php',
    type: 'POST',
    dataType: 'json',
    contentType: 'application/json',
    data: JSON.stringify({
        type: 'encrypted_link',
        path: 'pages/product/detail',
        query: {
            productId: '12345',
            from: 'h5'
        }
    }),
    success: function(response) {
        if (response.success) {
            $('#result').html('<a href="' + response.data.encrypted_link + '">打开小程序</a>');
        } else {
            alert('生成失败: ' + response.message);
        }
    },
    error: function() {
        alert('请求失败');
    }
});
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查参数格式和必填项 |
| 40001 | AppSecret错误 | 检查config.php中的配置 |
| 40013 | AppID不合法 | 检查AppID是否正确 |
| 42001 | access_token超时 | 系统会自动重新获取 |
| 45009 | 接口调用超过限制 | 等待限制重置或联系微信 |

### 错误响应示例

```json
{
    "success": false,
    "code": 400,
    "message": "获取access_token失败: AppSecret错误或者AppSecret不属于这个小程序",
    "data": null,
    "timestamp": 1703001234
}
```

## 最佳实践

1. **推荐使用明文URL Scheme**：无需调用微信接口，生成速度快，无次数限制
2. **缓存机制**：系统已内置access_token缓存，无需担心频繁调用
3. **错误重试**：建议在客户端实现重试机制
4. **参数验证**：调用前验证参数的有效性
5. **日志监控**：定期检查logs/wechat.log文件

## 性能优化

- 明文URL Scheme生成速度最快，推荐优先使用
- 加密类型需要调用微信接口，建议做好缓存
- 批量生成时建议使用type=all一次性获取所有类型

## 安全注意事项

- 不要在前端暴露AppSecret
- 建议对API接口做访问频率限制
- 定期检查日志文件，监控异常访问

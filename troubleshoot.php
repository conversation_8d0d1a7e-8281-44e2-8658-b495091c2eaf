<?php
/**
 * 故障排除页面
 * 帮助诊断和解决常见问题
 */

require_once 'config.php';

// 诊断结果
$diagnostics = [];

// 1. 检查PHP环境
$diagnostics['php'] = [
    'title' => 'PHP环境检查',
    'items' => []
];

$diagnostics['php']['items'][] = [
    'name' => 'PHP版本',
    'status' => version_compare(PHP_VERSION, '7.0.0', '>=') ? 'success' : 'error',
    'value' => PHP_VERSION,
    'requirement' => '>= 7.0.0'
];

$diagnostics['php']['items'][] = [
    'name' => 'cURL扩展',
    'status' => extension_loaded('curl') ? 'success' : 'error',
    'value' => extension_loaded('curl') ? '已安装' : '未安装',
    'requirement' => '必须安装'
];

$diagnostics['php']['items'][] = [
    'name' => 'JSON扩展',
    'status' => extension_loaded('json') ? 'success' : 'error',
    'value' => extension_loaded('json') ? '已安装' : '未安装',
    'requirement' => '必须安装'
];

// 2. 检查文件权限
$diagnostics['files'] = [
    'title' => '文件权限检查',
    'items' => []
];

$diagnostics['files']['items'][] = [
    'name' => '缓存目录',
    'status' => is_writable(CACHE_DIR) ? 'success' : 'error',
    'value' => is_dir(CACHE_DIR) ? (is_writable(CACHE_DIR) ? '可写' : '只读') : '不存在',
    'requirement' => '必须可写'
];

$diagnostics['files']['items'][] = [
    'name' => '日志目录',
    'status' => is_writable(LOG_DIR) ? 'success' : 'error',
    'value' => is_dir(LOG_DIR) ? (is_writable(LOG_DIR) ? '可写' : '只读') : '不存在',
    'requirement' => '必须可写'
];

// 3. 检查配置
$diagnostics['config'] = [
    'title' => '配置检查',
    'items' => []
];

$diagnostics['config']['items'][] = [
    'name' => 'AppID格式',
    'status' => preg_match('/^wx[a-f0-9]{16}$/', WECHAT_APPID) ? 'success' : 'warning',
    'value' => WECHAT_APPID,
    'requirement' => '格式: wx + 16位字符'
];

$diagnostics['config']['items'][] = [
    'name' => 'AppSecret长度',
    'status' => strlen(WECHAT_APP_SECRET) === 32 ? 'success' : 'warning',
    'value' => strlen(WECHAT_APP_SECRET) . ' 字符',
    'requirement' => '32字符'
];

// 4. 测试网络连接
$diagnostics['network'] = [
    'title' => '网络连接测试',
    'items' => []
];

// 测试微信API连通性
$testUrl = 'https://api.weixin.qq.com/cgi-bin/token';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_NOBODY, true);
$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$diagnostics['network']['items'][] = [
    'name' => '微信API连通性',
    'status' => $httpCode === 200 ? 'success' : 'error',
    'value' => $httpCode ? "HTTP {$httpCode}" : '连接失败',
    'requirement' => 'HTTP 200'
];

// 5. 测试access_token
$diagnostics['token'] = [
    'title' => 'Access Token测试',
    'items' => []
];

try {
    // 测试稳定版access_token
    $stableToken = getStableAccessToken(WECHAT_APPID, WECHAT_APP_SECRET);
    $diagnostics['token']['items'][] = [
        'name' => '稳定版Access Token',
        'status' => 'success',
        'value' => '获取成功 (' . substr($stableToken, 0, 20) . '...)',
        'requirement' => '能够正常获取'
    ];
} catch (Exception $e) {
    $diagnostics['token']['items'][] = [
        'name' => '稳定版Access Token',
        'status' => 'error',
        'value' => $e->getMessage(),
        'requirement' => '能够正常获取'
    ];
}

// 6. 检查缓存状态
$diagnostics['cache'] = [
    'title' => '缓存状态',
    'items' => []
];

if (file_exists(ACCESS_TOKEN_CACHE_FILE)) {
    $cacheData = json_decode(file_get_contents(ACCESS_TOKEN_CACHE_FILE), true);
    if ($cacheData) {
        $isExpired = time() >= $cacheData['expires_at'];
        $tokenType = isset($cacheData['is_stable']) && $cacheData['is_stable'] ? '稳定版' : '普通版';
        
        $diagnostics['cache']['items'][] = [
            'name' => '缓存文件',
            'status' => $isExpired ? 'warning' : 'success',
            'value' => $isExpired ? '已过期' : '有效',
            'requirement' => '有效且未过期'
        ];
        
        $diagnostics['cache']['items'][] = [
            'name' => 'Token类型',
            'status' => 'info',
            'value' => $tokenType,
            'requirement' => '稳定版优先'
        ];
        
        $diagnostics['cache']['items'][] = [
            'name' => '过期时间',
            'status' => 'info',
            'value' => date('Y-m-d H:i:s', $cacheData['expires_at']),
            'requirement' => '未来时间'
        ];
    }
} else {
    $diagnostics['cache']['items'][] = [
        'name' => '缓存文件',
        'status' => 'warning',
        'value' => '不存在',
        'requirement' => '首次运行正常'
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排除 - 微信小程序跳转工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .diagnostic-section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            color: #495057;
        }
        
        .diagnostic-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .diagnostic-item:last-child {
            border-bottom: none;
        }
        
        .item-name {
            flex: 1;
            font-weight: 500;
            color: #333;
        }
        
        .item-value {
            flex: 2;
            color: #666;
            margin: 0 15px;
        }
        
        .item-requirement {
            flex: 1;
            color: #888;
            font-size: 14px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 故障排除</h1>
        
        <div class="summary">
            <h3>📊 诊断摘要</h3>
            <p>以下是系统环境和配置的详细检查结果。如果发现错误项目，请根据建议进行修复。</p>
        </div>
        
        <?php foreach ($diagnostics as $section): ?>
        <div class="diagnostic-section">
            <div class="section-header">
                <?php echo htmlspecialchars($section['title']); ?>
            </div>
            
            <?php foreach ($section['items'] as $item): ?>
            <div class="diagnostic-item">
                <div class="item-name"><?php echo htmlspecialchars($item['name']); ?></div>
                <div class="item-value"><?php echo htmlspecialchars($item['value']); ?></div>
                <div class="item-requirement"><?php echo htmlspecialchars($item['requirement']); ?></div>
                <span class="status-badge status-<?php echo $item['status']; ?>">
                    <?php 
                    $statusText = [
                        'success' => '✅ 正常',
                        'error' => '❌ 错误', 
                        'warning' => '⚠️ 警告',
                        'info' => 'ℹ️ 信息'
                    ];
                    echo $statusText[$item['status']] ?? $item['status'];
                    ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endforeach; ?>
        
        <div class="actions">
            <h3>🛠️ 快速操作</h3>
            <a href="clear_cache.php" class="btn btn-danger">清理缓存</a>
            <a href="test.php" class="btn btn-success">功能测试</a>
            <a href="index.php" class="btn btn-secondary">返回主页</a>
            <a href="?refresh=1" class="btn">刷新诊断</a>
        </div>
        
        <div class="summary">
            <h3>💡 常见问题解决方案</h3>
            <ul>
                <li><strong>40001错误</strong>：AppSecret错误或access_token无效，请检查配置并清理缓存</li>
                <li><strong>85079错误</strong>：小程序没有线上版本，请先在微信公众平台发布小程序</li>
                <li><strong>网络连接失败</strong>：检查服务器网络设置和防火墙配置</li>
                <li><strong>文件权限错误</strong>：确保cache和logs目录有写入权限</li>
                <li><strong>明文Scheme无法使用</strong>：需要在小程序后台配置允许的页面路径</li>
            </ul>
        </div>
    </div>
</body>
</html>

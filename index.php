<?php
/**
 * 微信小程序H5跳转页面
 * 支持明文URL Scheme、加密URL Scheme、加密URL Link三种方式
 */

// 引入配置文件
require_once 'config.php';

// 小程序配置信息
define('APPID', WECHAT_APPID);
define('APP_SECRET', WECHAT_APP_SECRET);

class WechatMiniProgram {
    private $appid;
    private $appSecret;
    private $accessToken;
    
    public function __construct($appid, $appSecret) {
        $this->appid = $appid;
        $this->appSecret = $appSecret;
    }
    
    /**
     * 获取access_token
     */
    private function getAccessToken() {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        // 先尝试从缓存获取
        $cachedToken = getCachedAccessToken();
        if ($cachedToken) {
            $this->accessToken = $cachedToken;
            return $this->accessToken;
        }

        // 缓存中没有，优先使用稳定版access_token
        try {
            $this->accessToken = getStableAccessToken($this->appid, $this->appSecret);
            return $this->accessToken;
        } catch (Exception $e) {
            writeLog("Failed to get stable access token: " . $e->getMessage(), 'WARNING');

            // 稳定版失败，尝试普通版本
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appSecret}";
            $response = $this->httpGet($url);
            $data = json_decode($response, true);

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                // 缓存普通access_token
                cacheAccessToken($data['access_token'], $data['expires_in'] ?? 7200, false);
                writeLog("Successfully obtained regular access token as fallback");
                return $this->accessToken;
            }

            $errorMsg = '获取access_token失败: ';
            if (isset($data['errcode'])) {
                $errorMsg .= getErrorMessage($data['errcode']);
            } else {
                $errorMsg .= $response;
            }
            writeLog($errorMsg, 'ERROR');
            throw new Exception($errorMsg);
        }
    }
    
    /**
     * 生成明文URL Scheme
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型 release|trial|develop
     */
    public function generatePlainUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $queryString = '';
        if (!empty($query)) {
            $queryString = http_build_query($query);
        }
        
        $scheme = "weixin://dl/business/?appid={$this->appid}";
        if ($path) {
            $scheme .= "&path=" . urlencode($path);
        }
        if ($queryString) {
            $scheme .= "&query=" . urlencode($queryString);
        }
        $scheme .= "&env_version={$envVersion}";
        
        return $scheme;
    }
    
    /**
     * 生成加密URL Scheme
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型
     */
    public function generateEncryptedUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generatescheme?access_token={$accessToken}";
        
        $data = [
            'jump_wxa' => [
                'path' => $path,
                'query' => http_build_query($query),
                'env_version' => $envVersion
            ],
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['openlink'])) {
            return $result['openlink'];
        }
        
        throw new Exception('生成加密URL Scheme失败: ' . $response);
    }
    
    /**
     * 生成加密URL Link
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型
     */
    public function generateEncryptedUrlLink($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={$accessToken}";
        
        $data = [
            'path' => $path,
            'query' => http_build_query($query),
            'env_version' => $envVersion,
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['url_link'])) {
            return $result['url_link'];
        }
        
        throw new Exception('生成加密URL Link失败: ' . $response);
    }
    
    /**
     * HTTP GET请求
     */
    private function httpGet($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
}

// 处理请求
$wechat = new WechatMiniProgram(APPID, APP_SECRET);
$error = '';
$results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $path = $_POST['path'] ?? '';
        $queryParams = [];
        
        // 解析查询参数
        if (!empty($_POST['query'])) {
            parse_str($_POST['query'], $queryParams);
        }
        
        $envVersion = $_POST['env_version'] ?? 'release';
        $type = $_POST['type'] ?? 'plain';
        
        switch ($type) {
            case 'plain':
                $results['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $queryParams, $envVersion);
                break;
            case 'encrypted_scheme':
                $results['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $queryParams, $envVersion);
                break;
            case 'encrypted_link':
                $results['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $queryParams, $envVersion);
                break;
            case 'all':
                $results['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $queryParams, $envVersion);
                $results['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $queryParams, $envVersion);
                $results['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $queryParams, $envVersion);
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序H5跳转工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background: #07c160;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #06ad56;
        }
        .error {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .results {
            margin-top: 30px;
        }
        .result-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #07c160;
        }
        .result-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result-url {
            word-break: break-all;
            background: white;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
        }
        .copy-btn {
            background: #1989fa;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 5px;
            font-size: 12px;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #1989fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序H5跳转工具</h1>
        
        <div class="info">
            <strong>当前小程序信息：</strong><br>
            AppID: <?php echo APPID; ?><br>
            <small>支持明文URL Scheme、加密URL Scheme、加密URL Link三种跳转方式</small><br>
            <small style="margin-top: 10px; display: block;">
                <a href="test.php" style="color: #1989fa;">功能测试</a> |
                <a href="troubleshoot.php" style="color: #1989fa;">故障排除</a> |
                <a href="demo.html" style="color: #1989fa;">使用演示</a> |
                <a href="clear_cache.php" style="color: #dc3545;">清理缓存</a>
            </small>
        </div>

        <form method="POST">
            <div class="form-group">
                <label for="type">跳转类型：</label>
                <select name="type" id="type">
                    <option value="plain" <?php echo ($_POST['type'] ?? '') === 'plain' ? 'selected' : ''; ?>>明文URL Scheme（推荐）</option>
                    <option value="encrypted_scheme" <?php echo ($_POST['type'] ?? '') === 'encrypted_scheme' ? 'selected' : ''; ?>>加密URL Scheme</option>
                    <option value="encrypted_link" <?php echo ($_POST['type'] ?? '') === 'encrypted_link' ? 'selected' : ''; ?>>加密URL Link</option>
                    <option value="all" <?php echo ($_POST['type'] ?? '') === 'all' ? 'selected' : ''; ?>>生成所有类型</option>
                </select>
            </div>

            <div class="form-group">
                <label for="path">小程序页面路径：</label>
                <input type="text" name="path" id="path" placeholder="例如：pages/index/index" value="<?php echo htmlspecialchars($_POST['path'] ?? ''); ?>">
                <small>留空则跳转到小程序首页</small>
            </div>

            <div class="form-group">
                <label for="query">查询参数：</label>
                <textarea name="query" id="query" placeholder="例如：id=123&name=test"><?php echo htmlspecialchars($_POST['query'] ?? ''); ?></textarea>
                <small>格式：key1=value1&key2=value2</small>
            </div>

            <div class="form-group">
                <label for="env_version">版本类型：</label>
                <select name="env_version" id="env_version">
                    <option value="release" <?php echo ($_POST['env_version'] ?? 'release') === 'release' ? 'selected' : ''; ?>>正式版</option>
                    <option value="trial" <?php echo ($_POST['env_version'] ?? '') === 'trial' ? 'selected' : ''; ?>>体验版</option>
                    <option value="develop" <?php echo ($_POST['env_version'] ?? '') === 'develop' ? 'selected' : ''; ?>>开发版</option>
                </select>
            </div>

            <button type="submit">生成跳转链接</button>
        </form>

        <?php if ($error): ?>
            <div class="error">
                <strong>错误：</strong><?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($results)): ?>
            <div class="results">
                <h3>生成的跳转链接：</h3>
                
                <?php if (isset($results['plain_scheme'])): ?>
                    <div class="result-item">
                        <div class="result-title">明文URL Scheme（推荐使用）</div>
                        <div class="result-url" id="plain_scheme"><?php echo htmlspecialchars($results['plain_scheme']); ?></div>
                        <button class="copy-btn" onclick="copyToClipboard('plain_scheme')">复制链接</button>
                        <button class="copy-btn" onclick="openLink('<?php echo htmlspecialchars($results['plain_scheme']); ?>')">测试打开</button>
                    </div>
                <?php endif; ?>

                <?php if (isset($results['encrypted_scheme'])): ?>
                    <div class="result-item">
                        <div class="result-title">加密URL Scheme</div>
                        <div class="result-url" id="encrypted_scheme"><?php echo htmlspecialchars($results['encrypted_scheme']); ?></div>
                        <button class="copy-btn" onclick="copyToClipboard('encrypted_scheme')">复制链接</button>
                        <button class="copy-btn" onclick="openLink('<?php echo htmlspecialchars($results['encrypted_scheme']); ?>')">测试打开</button>
                    </div>
                <?php endif; ?>

                <?php if (isset($results['encrypted_link'])): ?>
                    <div class="result-item">
                        <div class="result-title">加密URL Link</div>
                        <div class="result-url" id="encrypted_link"><?php echo htmlspecialchars($results['encrypted_link']); ?></div>
                        <button class="copy-btn" onclick="copyToClipboard('encrypted_link')">复制链接</button>
                        <button class="copy-btn" onclick="openLink('<?php echo htmlspecialchars($results['encrypted_link']); ?>')">测试打开</button>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('链接已复制到剪贴板');
                });
            } else {
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('链接已复制到剪贴板');
            }
        }
        
        function openLink(url) {
            window.open(url, '_blank');
        }
    </script>
</body>
</html>

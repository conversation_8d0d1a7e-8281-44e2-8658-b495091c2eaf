<?php
/**
 * 微信小程序跳转API接口
 * 提供JSON格式的API调用方式
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'config.php';

class WechatMiniProgramAPI {
    private $appid;
    private $appSecret;
    private $accessToken;
    
    public function __construct($appid, $appSecret) {
        $this->appid = $appid;
        $this->appSecret = $appSecret;
    }
    
    /**
     * 获取access_token
     */
    private function getAccessToken() {
        if ($this->accessToken) {
            return $this->accessToken;
        }
        
        // 先尝试从缓存获取
        $cachedToken = getCachedAccessToken();
        if ($cachedToken) {
            $this->accessToken = $cachedToken;
            return $this->accessToken;
        }
        
        // 缓存中没有，重新获取
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appSecret}";
        $response = $this->httpGet($url);
        $data = json_decode($response, true);
        
        if (isset($data['access_token'])) {
            $this->accessToken = $data['access_token'];
            cacheAccessToken($data['access_token'], $data['expires_in'] ?? 7200);
            writeLog("API: Successfully obtained new access token");
            return $this->accessToken;
        }
        
        throw new Exception('获取access_token失败: ' . (isset($data['errcode']) ? getErrorMessage($data['errcode']) : $response));
    }
    
    /**
     * 生成明文URL Scheme
     */
    public function generatePlainUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $queryString = '';
        if (!empty($query)) {
            $queryString = is_array($query) ? http_build_query($query) : $query;
        }
        
        $scheme = "weixin://dl/business/?appid={$this->appid}";
        if ($path) {
            $scheme .= "&path=" . urlencode($path);
        }
        if ($queryString) {
            $scheme .= "&query=" . urlencode($queryString);
        }
        $scheme .= "&env_version={$envVersion}";
        
        return $scheme;
    }
    
    /**
     * 生成加密URL Scheme
     */
    public function generateEncryptedUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generatescheme?access_token={$accessToken}";
        
        $queryString = is_array($query) ? http_build_query($query) : $query;
        
        $data = [
            'jump_wxa' => [
                'path' => $path,
                'query' => $queryString,
                'env_version' => $envVersion
            ],
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['openlink'])) {
            return $result['openlink'];
        }
        
        throw new Exception('生成加密URL Scheme失败: ' . (isset($result['errcode']) ? getErrorMessage($result['errcode']) : $response));
    }
    
    /**
     * 生成加密URL Link
     */
    public function generateEncryptedUrlLink($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={$accessToken}";
        
        $queryString = is_array($query) ? http_build_query($query) : $query;
        
        $data = [
            'path' => $path,
            'query' => $queryString,
            'env_version' => $envVersion,
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['url_link'])) {
            return $result['url_link'];
        }
        
        throw new Exception('生成加密URL Link失败: ' . (isset($result['errcode']) ? getErrorMessage($result['errcode']) : $response));
    }
    
    /**
     * HTTP GET请求
     */
    private function httpGet($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
}

// 统一响应格式
function apiResponse($success, $data = null, $message = '', $code = 0) {
    return json_encode([
        'success' => $success,
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}

// 处理API请求
try {
    $method = $_SERVER['REQUEST_METHOD'];
    $input = null;
    
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
    } elseif ($method === 'GET') {
        $input = $_GET;
    } else {
        throw new Exception('不支持的请求方法');
    }
    
    if (!$input) {
        throw new Exception('请求参数为空');
    }
    
    // 验证必需参数
    $type = $input['type'] ?? 'plain';
    $path = $input['path'] ?? '';
    $query = $input['query'] ?? [];
    $envVersion = $input['env_version'] ?? 'release';
    
    // 如果query是字符串，尝试解析
    if (is_string($query) && !empty($query)) {
        parse_str($query, $parsedQuery);
        $query = $parsedQuery;
    }
    
    $wechat = new WechatMiniProgramAPI(WECHAT_APPID, WECHAT_APP_SECRET);
    $result = [];
    
    switch ($type) {
        case 'plain':
            $result['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $query, $envVersion);
            break;
            
        case 'encrypted_scheme':
            $result['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $query, $envVersion);
            break;
            
        case 'encrypted_link':
            $result['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $query, $envVersion);
            break;
            
        case 'all':
            $result['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $query, $envVersion);
            $result['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $query, $envVersion);
            $result['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $query, $envVersion);
            break;
            
        default:
            throw new Exception('不支持的类型: ' . $type);
    }
    
    writeLog("API: Successfully generated {$type} for path: {$path}");
    echo apiResponse(true, $result, '生成成功');
    
} catch (Exception $e) {
    writeLog("API Error: " . $e->getMessage(), 'ERROR');
    http_response_code(400);
    echo apiResponse(false, null, $e->getMessage(), 400);
}
?>

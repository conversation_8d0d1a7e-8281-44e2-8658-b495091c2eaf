<?php
/**
 * 清理缓存工具
 * 用于清理access_token缓存和日志文件
 */

require_once 'config.php';

// 检查是否通过命令行或GET参数确认清理
$confirm = false;
if (php_sapi_name() === 'cli') {
    // 命令行模式
    $confirm = isset($argv[1]) && $argv[1] === 'confirm';
} else {
    // Web模式
    $confirm = isset($_GET['confirm']) && $_GET['confirm'] === 'yes';
}

if (!$confirm) {
    if (php_sapi_name() === 'cli') {
        echo "使用方法: php clear_cache.php confirm\n";
        echo "这将清理所有缓存文件和日志文件\n";
    } else {
        echo "<!DOCTYPE html>";
        echo "<html><head><meta charset='UTF-8'><title>清理缓存</title></head><body>";
        echo "<h2>清理缓存工具</h2>";
        echo "<p>这将清理以下内容：</p>";
        echo "<ul>";
        echo "<li>Access Token缓存文件</li>";
        echo "<li>日志文件</li>";
        echo "</ul>";
        echo "<p><strong>注意：清理后需要重新获取access_token</strong></p>";
        echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>确认清理</a></p>";
        echo "<p><a href='index.php'>返回主页</a></p>";
        echo "</body></html>";
    }
    exit;
}

$results = [];

// 清理access_token缓存
if (file_exists(ACCESS_TOKEN_CACHE_FILE)) {
    if (unlink(ACCESS_TOKEN_CACHE_FILE)) {
        $results[] = "✅ Access Token缓存文件已清理";
        writeLog("Cache cleared: access_token cache file deleted");
    } else {
        $results[] = "❌ 清理Access Token缓存文件失败";
    }
} else {
    $results[] = "ℹ️ Access Token缓存文件不存在";
}

// 清理日志文件
if (file_exists(LOG_FILE)) {
    if (unlink(LOG_FILE)) {
        $results[] = "✅ 日志文件已清理";
    } else {
        $results[] = "❌ 清理日志文件失败";
    }
} else {
    $results[] = "ℹ️ 日志文件不存在";
}

// 清理缓存目录中的其他文件
if (is_dir(CACHE_DIR)) {
    $files = glob(CACHE_DIR . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            if (unlink($file)) {
                $results[] = "✅ 已清理缓存文件: " . basename($file);
            } else {
                $results[] = "❌ 清理缓存文件失败: " . basename($file);
            }
        }
    }
}

// 重新创建日志文件并记录清理操作
writeLog("Cache and logs cleared by clear_cache.php");

if (php_sapi_name() === 'cli') {
    // 命令行输出
    echo "缓存清理完成:\n";
    foreach ($results as $result) {
        echo $result . "\n";
    }
    echo "\n建议操作:\n";
    echo "1. 检查小程序AppID和AppSecret是否正确\n";
    echo "2. 确认小程序已发布线上版本\n";
    echo "3. 重新测试功能\n";
} else {
    // Web输出
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>清理完成</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }";
    echo ".result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".success { color: #155724; background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }";
    echo "</style></head><body>";
    
    echo "<h2>🧹 缓存清理完成</h2>";
    
    echo "<div class='result success'>";
    foreach ($results as $result) {
        echo "<p>" . htmlspecialchars($result) . "</p>";
    }
    echo "</div>";
    
    echo "<h3>📋 建议操作</h3>";
    echo "<ol>";
    echo "<li>检查小程序AppID和AppSecret是否正确</li>";
    echo "<li>确认小程序已发布线上版本</li>";
    echo "<li>在小程序后台配置明文scheme允许的页面</li>";
    echo "<li>重新测试功能</li>";
    echo "</ol>";
    
    echo "<h3>🔧 快速操作</h3>";
    echo "<a href='test.php' class='btn'>功能测试</a>";
    echo "<a href='index.php' class='btn'>返回主页</a>";
    echo "<a href='demo.html' class='btn'>演示页面</a>";
    
    echo "<h3>📊 当前状态</h3>";
    echo "<div class='result'>";
    echo "<p><strong>缓存目录:</strong> " . (is_dir(CACHE_DIR) ? '✅ 存在' : '❌ 不存在') . "</p>";
    echo "<p><strong>日志目录:</strong> " . (is_dir(LOG_DIR) ? '✅ 存在' : '❌ 不存在') . "</p>";
    echo "<p><strong>缓存文件:</strong> " . (file_exists(ACCESS_TOKEN_CACHE_FILE) ? '❌ 存在' : '✅ 已清理') . "</p>";
    echo "<p><strong>日志文件:</strong> " . (file_exists(LOG_FILE) ? '✅ 已重建' : '✅ 已清理') . "</p>";
    echo "</div>";
    
    echo "</body></html>";
}
?>

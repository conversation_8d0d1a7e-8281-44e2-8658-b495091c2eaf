<?php
/**
 * 微信小程序跳转功能测试页面
 */

require_once 'config.php';

// 测试用的小程序信息
$appid = 'wx692c3a5888dc4c96';
$appSecret = 'f303437014ad65fc8049b7dc821cbb20';

echo "<h2>微信小程序跳转功能测试</h2>";

// 测试明文URL Scheme生成
echo "<h3>1. 明文URL Scheme测试</h3>";

function generatePlainUrlScheme($appid, $path = '', $query = [], $envVersion = 'release') {
    $queryString = '';
    if (!empty($query)) {
        $queryString = http_build_query($query);
    }
    
    $scheme = "weixin://dl/business/?appid={$appid}";
    if ($path) {
        $scheme .= "&path=" . urlencode($path);
    }
    if ($queryString) {
        $scheme .= "&query=" . urlencode($queryString);
    }
    $scheme .= "&env_version={$envVersion}";
    
    return $scheme;
}

// 测试不同场景的明文URL Scheme
$testCases = [
    [
        'name' => '跳转到首页',
        'path' => '',
        'query' => [],
        'env_version' => 'release'
    ],
    [
        'name' => '跳转到指定页面',
        'path' => 'pages/detail/detail',
        'query' => [],
        'env_version' => 'release'
    ],
    [
        'name' => '跳转到指定页面并传参',
        'path' => 'pages/detail/detail',
        'query' => ['id' => '123', 'type' => 'product'],
        'env_version' => 'release'
    ],
    [
        'name' => '跳转到体验版',
        'path' => 'pages/index/index',
        'query' => ['debug' => '1'],
        'env_version' => 'trial'
    ]
];

foreach ($testCases as $case) {
    $scheme = generatePlainUrlScheme($appid, $case['path'], $case['query'], $case['env_version']);
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<strong>{$case['name']}:</strong><br>";
    echo "<code style='word-break: break-all; background: #f5f5f5; padding: 5px;'>{$scheme}</code><br>";
    echo "<a href='{$scheme}' target='_blank' style='color: #07c160;'>点击测试</a>";
    echo "</div>";
}

// 测试access_token获取
echo "<h3>2. Access Token测试</h3>";

function testAccessToken($appid, $appSecret) {
    $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appSecret}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

$tokenResult = testAccessToken($appid, $appSecret);

echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "<strong>HTTP状态码:</strong> {$tokenResult['http_code']}<br>";

if ($tokenResult['data']) {
    if (isset($tokenResult['data']['access_token'])) {
        echo "<strong style='color: green;'>✓ Access Token获取成功</strong><br>";
        echo "<strong>Token:</strong> " . substr($tokenResult['data']['access_token'], 0, 20) . "...<br>";
        echo "<strong>过期时间:</strong> {$tokenResult['data']['expires_in']}秒<br>";
    } else {
        echo "<strong style='color: red;'>✗ Access Token获取失败</strong><br>";
        if (isset($tokenResult['data']['errcode'])) {
            echo "<strong>错误码:</strong> {$tokenResult['data']['errcode']}<br>";
            echo "<strong>错误信息:</strong> " . getErrorMessage($tokenResult['data']['errcode']) . "<br>";
        }
    }
} else {
    echo "<strong style='color: red;'>✗ 响应解析失败</strong><br>";
    echo "<strong>原始响应:</strong> {$tokenResult['response']}<br>";
}
echo "</div>";

// 显示配置信息
echo "<h3>3. 配置信息</h3>";
echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "<strong>AppID:</strong> {$appid}<br>";
echo "<strong>AppSecret:</strong> " . substr($appSecret, 0, 10) . "...<br>";
echo "<strong>缓存目录:</strong> " . (is_dir(CACHE_DIR) ? '✓ 存在' : '✗ 不存在') . "<br>";
echo "<strong>日志目录:</strong> " . (is_dir(LOG_DIR) ? '✓ 存在' : '✗ 不存在') . "<br>";
echo "</div>";

// 显示使用说明
echo "<h3>4. 使用说明</h3>";
echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>明文URL Scheme</strong>：无需调用接口，直接拼接即可使用，推荐使用</li>";
echo "<li><strong>加密URL Scheme</strong>：需要调用微信接口生成，有生成次数限制</li>";
echo "<li><strong>加密URL Link</strong>：需要调用微信接口生成，可在微信外使用</li>";
echo "<li><strong>注意事项</strong>：";
echo "<ul>";
echo "<li>明文URL Scheme需要在小程序后台配置允许的页面路径</li>";
echo "<li>每个小程序每天总打开次数上限为300万次</li>";
echo "<li>仅支持非个人主体小程序使用</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

// 显示场景值说明
echo "<h3>5. 场景值说明</h3>";
echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "<ul>";
echo "<li><strong>明文URL Scheme</strong>：场景值 1286</li>";
echo "<li><strong>加密URL Scheme</strong>：场景值 1065</li>";
echo "<li><strong>加密URL Link（微信外）</strong>：场景值 1194</li>";
echo "<li><strong>加密URL Link（微信内）</strong>：场景值 1167</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='index.php' style='color: #07c160; text-decoration: none;'>← 返回主页面</a></p>";
?>

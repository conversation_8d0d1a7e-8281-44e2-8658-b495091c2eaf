<?php
/**
 * 微信小程序配置文件
 */

// 小程序基本信息
define('WECHAT_APPID', 'wx692c3a5888dc4c96');
define('WECHAT_APP_SECRET', 'f303437014ad65fc8049b7dc821cbb20');

// 缓存配置
define('CACHE_DIR', __DIR__ . '/cache');
define('ACCESS_TOKEN_CACHE_FILE', CACHE_DIR . '/access_token.json');
define('ACCESS_TOKEN_EXPIRE_TIME', 7200); // access_token有效期2小时

// 日志配置
define('LOG_DIR', __DIR__ . '/logs');
define('LOG_FILE', LOG_DIR . '/wechat.log');

// 创建必要的目录
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

if (!is_dir(LOG_DIR)) {
    mkdir(LOG_DIR, 0755, true);
}

/**
 * 日志记录函数
 */
function writeLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 缓存access_token
 */
function cacheAccessToken($accessToken, $expiresIn) {
    $data = [
        'access_token' => $accessToken,
        'expires_at' => time() + $expiresIn - 300 // 提前5分钟过期
    ];
    file_put_contents(ACCESS_TOKEN_CACHE_FILE, json_encode($data), LOCK_EX);
    writeLog("Access token cached, expires at: " . date('Y-m-d H:i:s', $data['expires_at']));
}

/**
 * 获取缓存的access_token
 */
function getCachedAccessToken() {
    if (!file_exists(ACCESS_TOKEN_CACHE_FILE)) {
        return null;
    }
    
    $data = json_decode(file_get_contents(ACCESS_TOKEN_CACHE_FILE), true);
    if (!$data || !isset($data['access_token']) || !isset($data['expires_at'])) {
        return null;
    }
    
    if (time() >= $data['expires_at']) {
        writeLog("Cached access token expired");
        return null;
    }
    
    writeLog("Using cached access token");
    return $data['access_token'];
}

/**
 * 错误码映射
 */
function getErrorMessage($errcode) {
    $errorMessages = [
        40001 => 'AppSecret错误或者AppSecret不属于这个小程序',
        40002 => '请确保grant_type字段值为client_credential',
        40013 => '不合法的AppID',
        40125 => '小程序配置无效',
        41002 => '缺少appid参数',
        41004 => '缺少secret参数',
        42001 => 'access_token超时',
        43104 => 'appid与openid不匹配',
        45009 => '接口调用超过限制',
        48001 => 'api功能未授权',
        85079 => '小程序没有线上版本',
        85080 => '小程序没有审核版本',
        85081 => '该版本不能灰度',
        85082 => '小程序没有线上版本',
        85083 => '该版本不能回退',
        85084 => '该版本不能回退',
        85085 => '当前版本不能回退',
        85086 => '当前版本不能回退',
        85087 => '该版本不能设置为体验版',
        85088 => '该版本不能设置为体验版'
    ];
    
    return isset($errorMessages[$errcode]) ? $errorMessages[$errcode] : "未知错误码: {$errcode}";
}
?>

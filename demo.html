<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序跳转演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #07c160, #06ad56);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            border-left: 4px solid #07c160;
            padding-left: 15px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .demo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .demo-desc {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .demo-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #07c160;
            color: white;
        }
        
        .btn-primary:hover {
            background: #06ad56;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .result-area {
            background: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            min-height: 100px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        
        .loading {
            text-align: center;
            color: #6c757d;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            .demo-buttons {
                flex-direction: column;
            }
            
            .btn {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 微信小程序跳转演示</h1>
            <p>体验三种不同的小程序跳转方式</p>
        </div>
        
        <div class="content">
            <!-- 预设演示场景 -->
            <div class="demo-section">
                <h3>📱 预设演示场景</h3>
                
                <div class="demo-card">
                    <div class="demo-title">场景1：跳转到小程序首页</div>
                    <div class="demo-desc">最简单的跳转方式，直接打开小程序首页</div>
                    <div class="demo-buttons">
                        <button class="btn btn-primary" onclick="generateDemo('plain', '', {})">明文Scheme</button>
                        <button class="btn btn-secondary" onclick="generateDemo('encrypted_scheme', '', {})">加密Scheme</button>
                        <button class="btn btn-info" onclick="generateDemo('encrypted_link', '', {})">加密Link</button>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="demo-title">场景2：跳转到商品详情页</div>
                    <div class="demo-desc">跳转到指定页面并传递商品ID参数</div>
                    <div class="demo-buttons">
                        <button class="btn btn-primary" onclick="generateDemo('plain', 'pages/product/detail', {id: '12345', from: 'h5'})">明文Scheme</button>
                        <button class="btn btn-secondary" onclick="generateDemo('encrypted_scheme', 'pages/product/detail', {id: '12345', from: 'h5'})">加密Scheme</button>
                        <button class="btn btn-info" onclick="generateDemo('encrypted_link', 'pages/product/detail', {id: '12345', from: 'h5'})">加密Link</button>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="demo-title">场景3：跳转到用户中心</div>
                    <div class="demo-desc">跳转到用户中心页面，传递用户相关参数</div>
                    <div class="demo-buttons">
                        <button class="btn btn-primary" onclick="generateDemo('plain', 'pages/user/profile', {userId: '67890', tab: 'orders'})">明文Scheme</button>
                        <button class="btn btn-secondary" onclick="generateDemo('encrypted_scheme', 'pages/user/profile', {userId: '67890', tab: 'orders'})">加密Scheme</button>
                        <button class="btn btn-info" onclick="generateDemo('encrypted_link', 'pages/user/profile', {userId: '67890', tab: 'orders'})">加密Link</button>
                    </div>
                </div>
            </div>
            
            <!-- 自定义生成 -->
            <div class="demo-section">
                <h3>🛠️ 自定义生成</h3>
                
                <div class="demo-card">
                    <div class="demo-title">自定义参数生成跳转链接</div>
                    
                    <div class="form-group">
                        <label for="customPath">页面路径：</label>
                        <input type="text" id="customPath" placeholder="例如：pages/index/index" value="">
                    </div>
                    
                    <div class="form-group">
                        <label for="customQuery">查询参数：</label>
                        <input type="text" id="customQuery" placeholder="例如：id=123&name=test" value="">
                    </div>
                    
                    <div class="form-group">
                        <label for="customEnv">版本类型：</label>
                        <select id="customEnv">
                            <option value="release">正式版</option>
                            <option value="trial">体验版</option>
                            <option value="develop">开发版</option>
                        </select>
                    </div>
                    
                    <div class="demo-buttons">
                        <button class="btn btn-primary" onclick="generateCustom('plain')">生成明文Scheme</button>
                        <button class="btn btn-secondary" onclick="generateCustom('encrypted_scheme')">生成加密Scheme</button>
                        <button class="btn btn-info" onclick="generateCustom('encrypted_link')">生成加密Link</button>
                        <button class="btn btn-primary" onclick="generateCustom('all')" style="background: #fd7e14;">生成所有类型</button>
                    </div>
                </div>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="demo-section">
                <h3>📋 生成结果</h3>
                <div id="resultArea" class="result-area">
                    <div class="loading">点击上方按钮生成跳转链接...</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>💡 提示：明文URL Scheme无需调用接口，生成速度最快，推荐优先使用</p>
            <p><a href="index.php" style="color: #07c160;">返回管理页面</a> | <a href="test.php" style="color: #07c160;">功能测试</a></p>
        </div>
    </div>

    <script>
        // 生成演示链接
        function generateDemo(type, path, query) {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = '<div class="loading">正在生成链接...</div>';
            
            fetch('api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: type,
                    path: path,
                    query: query,
                    env_version: 'release'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResult(data.data, type, path, query);
                } else {
                    resultArea.innerHTML = `<div class="error">生成失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultArea.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            });
        }
        
        // 生成自定义链接
        function generateCustom(type) {
            const path = document.getElementById('customPath').value;
            const queryStr = document.getElementById('customQuery').value;
            const envVersion = document.getElementById('customEnv').value;
            
            // 解析查询参数
            const query = {};
            if (queryStr) {
                queryStr.split('&').forEach(pair => {
                    const [key, value] = pair.split('=');
                    if (key && value) {
                        query[decodeURIComponent(key)] = decodeURIComponent(value);
                    }
                });
            }
            
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = '<div class="loading">正在生成链接...</div>';
            
            fetch('api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: type,
                    path: path,
                    query: query,
                    env_version: envVersion
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResult(data.data, type, path, query);
                } else {
                    resultArea.innerHTML = `<div class="error">生成失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultArea.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            });
        }
        
        // 显示结果
        function displayResult(data, type, path, query) {
            const resultArea = document.getElementById('resultArea');
            let html = '<div class="success">✅ 生成成功！</div>';
            
            html += `<div style="margin-top: 15px;"><strong>请求参数：</strong></div>`;
            html += `<div>类型: ${type}</div>`;
            html += `<div>路径: ${path || '(首页)'}</div>`;
            html += `<div>参数: ${JSON.stringify(query)}</div>`;
            html += `<hr style="margin: 15px 0;">`;
            
            Object.keys(data).forEach(key => {
                const url = data[key];
                const typeName = {
                    'plain_scheme': '明文URL Scheme',
                    'encrypted_scheme': '加密URL Scheme', 
                    'encrypted_link': '加密URL Link'
                }[key] || key;
                
                html += `<div style="margin-bottom: 20px;">`;
                html += `<div><strong>${typeName}:</strong></div>`;
                html += `<div style="background: white; padding: 10px; border-radius: 3px; margin: 5px 0; word-break: break-all;">${url}</div>`;
                html += `<button class="btn btn-primary" onclick="copyToClipboard('${url}')" style="margin-right: 10px;">复制链接</button>`;
                html += `<button class="btn btn-info" onclick="testOpen('${url}')">测试打开</button>`;
                html += `</div>`;
            });
            
            resultArea.innerHTML = html;
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('✅ 链接已复制到剪贴板');
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('✅ 链接已复制到剪贴板');
            }
        }
        
        // 测试打开
        function testOpen(url) {
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
